# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
FastAPI endpoints for Delta Lake querying with DuckDB.

This module provides REST API endpoints for:
- Query execution with Arrow streaming and JSON responses
- Table management and information
- Health checks and service status
- Named query execution
"""

import io
import json
import logging
import os
import time
from typing import Dict, List, Optional, Any

import pyarrow as pa
import pyarrow.ipc as ipc
from fastapi import FastAPI, HTTPException, Query, Depends, Request
from fastapi.responses import StreamingResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware

from data_marketplace.api.duckdb_service import DuckDBService, TableConfig, SafetyLimits, SafetyLimits, SafetyLimits
from data_marketplace.api.models import (
    QueryRequest, QueryResponse, QueryMetrics, ErrorResponse,
    TableInfo, HealthResponse, ResponseFormat, QueryTemplate,
    PREDEFINED_QUERIES
)
from data_marketplace.config.settings import Settings

logger = logging.getLogger(__name__)

# Global service instance
_duckdb_service: Optional[DuckDBService] = None


def get_duckdb_service() -> DuckDBService:
    """Dependency to get the DuckDB service instance."""
    global _duckdb_service
    if _duckdb_service is None:
        raise HTTPException(status_code=503, detail="Service not initialized")
    return _duckdb_service


def create_app(settings: Optional[Settings] = None) -> FastAPI:
    """Create and configure the FastAPI application."""
    if settings is None:
        settings = Settings()
    
    app = FastAPI(
        title="Data Marketplace Delta Lake API",
        description="High-performance API for querying Delta Lake tables using DuckDB",
        version="0.1.0",
        docs_url="/docs",
        redoc_url="/redoc"
    )
    
    # Add CORS middleware
    if settings.api.enable_cors:
        app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],  # Configure appropriately for production
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
    
    # Initialize global service with safety limits
    global _duckdb_service
    safety_limits = SafetyLimits(
        max_result_rows=int(os.getenv("MAX_RESULT_ROWS", "1000000")),
        max_result_bytes=int(os.getenv("MAX_RESULT_BYTES", str(100 * 1024 * 1024))),
        max_query_time_seconds=float(os.getenv("MAX_QUERY_TIME_SECONDS", "300")),
        max_concurrent_queries=int(os.getenv("MAX_CONCURRENT_QUERIES", "8")),
        queue_timeout_seconds=float(os.getenv("QUEUE_TIMEOUT_SECONDS", "30"))
    )
    _duckdb_service = DuckDBService(settings=settings, pool_size=4, safety_limits=safety_limits)
    
    @app.on_event("startup")
    async def startup_event():
        """Initialize the DuckDB service on startup."""
        logger.info("Initializing DuckDB service...")
        _duckdb_service.initialize()
        
        # Attach default tables if configured
        # This would typically be configured via environment variables
        # or a configuration file in production
        logger.info("DuckDB service initialized successfully")
    
    @app.on_event("shutdown")
    async def shutdown_event():
        """Clean up resources on shutdown."""
        logger.info("Shutting down DuckDB service...")
        if _duckdb_service:
            _duckdb_service.close()
        logger.info("DuckDB service shut down")
    
    return app


def create_arrow_streaming_response(arrow_table: pa.Table, metrics: QueryMetrics) -> StreamingResponse:
    """Create a streaming response with Apache Arrow IPC format."""
    
    def generate_arrow_stream():
        # Create a buffer to write the Arrow stream
        buffer = io.BytesIO()
        
        # Write the Arrow table as IPC stream
        with ipc.new_stream(buffer, arrow_table.schema) as writer:
            writer.write_table(arrow_table)
        
        # Add metrics as metadata in the stream
        buffer.seek(0)
        yield buffer.getvalue()
    
    return StreamingResponse(
        generate_arrow_stream(),
        media_type="application/vnd.apache.arrow.stream",
        headers={
            "X-Query-Time-Ms": str(metrics.execution_time_ms),
            "X-Rows-Returned": str(metrics.rows_returned),
            "X-Bytes-Scanned": str(metrics.bytes_scanned),
        }
    )


# API Endpoints

app = create_app()


@app.get("/health", response_model=HealthResponse)
async def health_check(service: DuckDBService = Depends(get_duckdb_service)):
    """Health check endpoint."""
    try:
        import duckdb
        
        return HealthResponse(
            status="healthy",
            version="0.1.0",
            duckdb_version=duckdb.__version__,
            attached_tables=list(service.attached_tables.keys()),
            pool_size=service.pool.pool_size
        )
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=503, detail="Service unhealthy")


@app.post("/q")
async def execute_query(
    request: QueryRequest,
    fmt: ResponseFormat = Query(ResponseFormat.ARROW, description="Response format"),
    service: DuckDBService = Depends(get_duckdb_service)
):
    """
    Execute a SQL query against attached Delta Lake tables.
    
    Supports both Apache Arrow streaming (default) and JSON responses.
    """
    try:
        # Validate query safety
        QueryTemplate.validate_query_safety(request.sql)
        
        # Substitute parameters if provided
        final_sql = QueryTemplate.substitute_parameters(request.sql, request.parameters)
        
        # Execute query
        result = service.execute_query(final_sql, timeout=request.timeout)
        
        # Create metrics
        metrics = QueryMetrics(
            execution_time_ms=result.execution_time_ms,
            rows_returned=len(result.arrow_table),
            bytes_scanned=result.bytes_scanned,
            snapshot_version=result.snapshot_version
        )
        
        if fmt == ResponseFormat.ARROW:
            # Return Arrow streaming response
            return create_arrow_streaming_response(result.arrow_table, metrics)
        else:
            # Return JSON response
            # Convert Arrow table to Python objects
            data = result.arrow_table.to_pylist()
            columns = result.arrow_table.column_names
            
            return QueryResponse(
                data=data,
                metrics=metrics,
                columns=columns
            )
            
    except ValueError as e:
        # Query validation error
        raise HTTPException(
            status_code=400,
            detail=ErrorResponse(
                error=str(e),
                error_type="ValidationError",
                query=request.sql
            ).dict()
        )
    except TimeoutError as e:
        # Query timeout or too many concurrent queries
        if "connection pool is full" in str(e) or "Too many concurrent queries" in str(e):
            raise HTTPException(
                status_code=429,
                detail=ErrorResponse(
                    error="Too many concurrent queries. Please try again later.",
                    error_type="ConcurrencyLimitError",
                    query=request.sql
                ).dict()
            )
        else:
            raise HTTPException(
                status_code=408,
                detail=ErrorResponse(
                    error=str(e),
                    error_type="TimeoutError",
                    query=request.sql
                ).dict()
            )
    except Exception as e:
        # Other errors
        logger.error(f"Query execution failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                error=str(e),
                error_type="ExecutionError",
                query=request.sql
            ).dict()
        )


@app.post("/q/named/{query_name}")
async def execute_named_query(
    query_name: str,
    parameters: Dict[str, Any],
    fmt: ResponseFormat = Query(ResponseFormat.ARROW, description="Response format"),
    service: DuckDBService = Depends(get_duckdb_service)
):
    """Execute a predefined named query with parameters."""
    try:
        if query_name not in PREDEFINED_QUERIES:
            raise HTTPException(
                status_code=404,
                detail=f"Named query '{query_name}' not found"
            )
        
        named_query = PREDEFINED_QUERIES[query_name]
        
        # Create query request
        request = QueryRequest(
            sql=named_query.sql,
            parameters=parameters
        )
        
        # Execute using the main query endpoint logic
        return await execute_query(request, fmt, service)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Named query execution failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/q/named")
async def list_named_queries():
    """List all available named queries."""
    return {
        "queries": [
            {
                "name": query.name,
                "description": query.description,
                "parameters": query.parameters
            }
            for query in PREDEFINED_QUERIES.values()
        ]
    }


@app.post("/tables/attach")
async def attach_table(
    alias: str,
    path: str,
    pin_snapshot: bool = True,
    service: DuckDBService = Depends(get_duckdb_service)
):
    """Attach a Delta Lake table."""
    try:
        table_config = TableConfig(
            name=alias,
            path=path,
            alias=alias,
            pin_snapshot=pin_snapshot
        )
        service.attach_table(table_config)
        return {"message": f"Table '{alias}' attached successfully"}
        
    except Exception as e:
        logger.error(f"Failed to attach table: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.delete("/tables/{alias}")
async def detach_table(
    alias: str,
    service: DuckDBService = Depends(get_duckdb_service)
):
    """Detach a Delta Lake table."""
    try:
        service.detach_table(alias)
        return {"message": f"Table '{alias}' detached successfully"}
        
    except Exception as e:
        logger.error(f"Failed to detach table: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/tables/{alias}/refresh")
async def refresh_table(
    alias: str,
    service: DuckDBService = Depends(get_duckdb_service)
):
    """Refresh a table's snapshot."""
    try:
        service.refresh_table_snapshot(alias)
        return {"message": f"Table '{alias}' snapshot refreshed successfully"}
        
    except Exception as e:
        logger.error(f"Failed to refresh table: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/tables/{alias}", response_model=TableInfo)
async def get_table_info(
    alias: str,
    service: DuckDBService = Depends(get_duckdb_service)
):
    """Get information about an attached table."""
    try:
        info = service.get_table_info(alias)
        return TableInfo(**info)
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to get table info: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/tables")
async def list_tables(service: DuckDBService = Depends(get_duckdb_service)):
    """List all attached tables."""
    return {
        "tables": [
            {
                "alias": alias,
                "path": config.path,
                "pin_snapshot": config.pin_snapshot
            }
            for alias, config in service.attached_tables.items()
        ]
    }
