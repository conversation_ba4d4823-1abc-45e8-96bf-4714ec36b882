# SPDX-FileCopyrightText: Terrafloww Labs, 2025

apiVersion: apps/v1
kind: Deployment
metadata:
  name: delta-lake-api
  labels:
    app: delta-lake-api
    component: api
spec:
  replicas: 2
  selector:
    matchLabels:
      app: delta-lake-api
      component: api
  template:
    metadata:
      labels:
        app: delta-lake-api
        component: api
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
    spec:
      containers:
      - name: api
        image: delta-lake-api:latest
        ports:
        - containerPort: 8000
          name: http
        env:
        - name: HOST
          value: "0.0.0.0"
        - name: PORT
          value: "8000"
        - name: WORKERS
          value: "1"
        - name: DUCKDB_MEMORY_LIMIT
          value: "3GB"
        - name: DUCKDB_THREADS
          value: "2"
        - name: DUCKDB_CACHE_DIR
          value: "/cache"
        - name: MAX_CONCURRENT_QUERIES
          value: "8"
        - name: MAX_RESULT_ROWS
          value: "1000000"
        - name: MAX_RESULT_BYTES
          value: "104857600"  # 100MB
        - name: SNAPSHOT_CHECK_INTERVAL
          value: "60"
        # S3 credentials from secret
        - name: AWS_ACCESS_KEY_ID
          valueFrom:
            secretKeyRef:
              name: s3-credentials
              key: access-key-id
              optional: true
        - name: AWS_SECRET_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: s3-credentials
              key: secret-access-key
              optional: true
        - name: AWS_REGION
          valueFrom:
            configMapKeyRef:
              name: api-config
              key: aws-region
              optional: true
        resources:
          requests:
            memory: "4Gi"
            cpu: "1000m"
            ephemeral-storage: "10Gi"
          limits:
            memory: "6Gi"
            cpu: "2000m"
            ephemeral-storage: "20Gi"
        volumeMounts:
        - name: cache-volume
          mountPath: /cache
        - name: tmp-volume
          mountPath: /tmp
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 5
          failureThreshold: 12
      volumes:
      - name: cache-volume
        emptyDir:
          sizeLimit: 15Gi
      - name: tmp-volume
        emptyDir:
          sizeLimit: 5Gi
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      terminationGracePeriodSeconds: 30

---
apiVersion: v1
kind: Service
metadata:
  name: delta-lake-api-service
  labels:
    app: delta-lake-api
    component: api
spec:
  selector:
    app: delta-lake-api
    component: api
  ports:
  - name: http
    port: 80
    targetPort: 8000
    protocol: TCP
  type: ClusterIP

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: api-config
data:
  aws-region: "us-west-2"
  log-level: "INFO"

---
# Example secret for S3 credentials (create manually with actual values)
apiVersion: v1
kind: Secret
metadata:
  name: s3-credentials
type: Opaque
data:
  # Base64 encoded values - replace with actual credentials
  # access-key-id: <base64-encoded-access-key>
  # secret-access-key: <base64-encoded-secret-key>

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: delta-lake-api-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: delta-lake-api
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  - type: Pods
    pods:
      metric:
        name: duckdb_queries_in_flight
      target:
        type: AverageValue
        averageValue: "8"
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
      - type: Pods
        value: 2
        periodSeconds: 60
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
