#!/usr/bin/env python3
# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
Demo script for the Delta Lake DuckDB API service.

This script demonstrates how to:
1. Start the API service
2. Attach Delta Lake tables
3. Execute queries with different formats
4. Monitor performance metrics
"""

import asyncio
import json
import tempfile
import time
from pathlib import Path

import httpx
import pyarrow as pa
import pyarrow.ipc as ipc
from deltalake import write_deltalake


async def create_demo_table() -> str:
    """Create a demo Delta Lake table for testing."""
    temp_dir = tempfile.mkdtemp()
    table_path = Path(temp_dir) / "demo_table"
    
    # Create sample data
    data = pa.table({
        'id': list(range(1, 1001)),
        'name': [f'User_{i}' for i in range(1, 1001)],
        'category': ['A', 'B', 'C'] * 333 + ['A'],
        'value': [i * 1.5 for i in range(1, 1001)],
        'timestamp': [f'2024-01-{(i % 30) + 1:02d}T12:00:00Z' for i in range(1, 1001)]
    })
    
    # Write as Delta table
    write_deltalake(str(table_path), data, mode="overwrite")
    print(f"Created demo table at: {table_path}")
    
    return str(table_path)


async def demo_api_usage():
    """Demonstrate API usage with various operations."""
    
    # API base URL
    base_url = "http://localhost:8000"
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        
        print("🚀 Starting Delta Lake API Demo")
        print("=" * 50)
        
        # 1. Check health
        print("\n1. Checking API health...")
        response = await client.get(f"{base_url}/health")
        if response.status_code == 200:
            health = response.json()
            print(f"✅ API is healthy - Version: {health['version']}")
            print(f"   DuckDB Version: {health['duckdb_version']}")
            print(f"   Pool Size: {health['pool_size']}")
        else:
            print(f"❌ API health check failed: {response.status_code}")
            return
        
        # 2. Create and attach demo table
        print("\n2. Creating demo table...")
        table_path = await create_demo_table()
        
        print("\n3. Attaching Delta table...")
        response = await client.post(
            f"{base_url}/tables/attach",
            params={
                "alias": "demo_table",
                "path": table_path,
                "pin_snapshot": True
            }
        )
        
        if response.status_code == 200:
            print("✅ Table attached successfully")
        else:
            print(f"❌ Failed to attach table: {response.status_code} - {response.text}")
            return
        
        # 3. Get table info
        print("\n4. Getting table information...")
        response = await client.get(f"{base_url}/tables/demo_table")
        if response.status_code == 200:
            table_info = response.json()
            print(f"✅ Table info retrieved:")
            print(f"   Alias: {table_info['alias']}")
            print(f"   Path: {table_info['path']}")
            print(f"   Row Count: {table_info['row_count']}")
            print(f"   Columns: {len(table_info['table_schema'])}")
        
        # 4. Execute JSON query
        print("\n5. Executing JSON query...")
        query_request = {
            "sql": "SELECT category, COUNT(*) as count, AVG(value) as avg_value FROM demo_table GROUP BY category ORDER BY count DESC",
            "timeout": 30.0
        }
        
        start_time = time.time()
        response = await client.post(
            f"{base_url}/q",
            params={"fmt": "json"},
            json=query_request
        )
        query_time = time.time() - start_time
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ JSON query completed in {query_time:.2f}s")
            print(f"   Rows returned: {result['metrics']['rows_returned']}")
            print(f"   Execution time: {result['metrics']['execution_time_ms']:.1f}ms")
            print(f"   Results:")
            for row in result['data']:
                print(f"     {row['category']}: {row['count']} items, avg value: {row['avg_value']:.2f}")
        else:
            print(f"❌ JSON query failed: {response.status_code} - {response.text}")
        
        # 5. Execute Arrow query
        print("\n6. Executing Arrow query...")
        query_request = {
            "sql": "SELECT * FROM demo_table WHERE category = 'A' AND value > 500 ORDER BY value DESC LIMIT 10",
            "timeout": 30.0
        }
        
        start_time = time.time()
        response = await client.post(
            f"{base_url}/q",
            params={"fmt": "arrow"},
            json=query_request
        )
        query_time = time.time() - start_time
        
        if response.status_code == 200:
            print(f"✅ Arrow query completed in {query_time:.2f}s")
            print(f"   Query time: {response.headers.get('X-Query-Time-Ms')}ms")
            print(f"   Rows returned: {response.headers.get('X-Rows-Returned')}")
            print(f"   Bytes scanned: {response.headers.get('X-Bytes-Scanned')}")
            
            # Parse Arrow response
            buffer = response.content
            reader = ipc.open_stream(buffer)
            table = reader.read_all()
            
            print(f"   Arrow table shape: {table.num_rows} rows x {table.num_columns} columns")
            print(f"   Sample data:")
            df = table.to_pandas()
            print(df.head(3).to_string(index=False))
        else:
            print(f"❌ Arrow query failed: {response.status_code} - {response.text}")
        
        # 6. Test parameterized query
        print("\n7. Testing parameterized query...")
        query_request = {
            "sql": "SELECT * FROM demo_table WHERE category IN {categories} AND value BETWEEN {min_val} AND {max_val} LIMIT {limit}",
            "parameters": {
                "categories": ["A", "B"],
                "min_val": 100,
                "max_val": 200,
                "limit": 5
            },
            "timeout": 30.0
        }
        
        response = await client.post(
            f"{base_url}/q",
            params={"fmt": "json"},
            json=query_request
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Parameterized query completed")
            print(f"   Rows returned: {len(result['data'])}")
            print(f"   Sample results:")
            for row in result['data'][:3]:
                print(f"     ID {row['id']}: {row['name']} (category {row['category']}, value {row['value']})")
        
        # 7. Test named queries
        print("\n8. Testing named queries...")
        response = await client.get(f"{base_url}/q/named")
        if response.status_code == 200:
            named_queries = response.json()
            print(f"✅ Available named queries: {len(named_queries['queries'])}")
            for query in named_queries['queries'][:3]:
                print(f"   - {query['name']}: {query['description']}")
        
        # 8. Check monitoring
        print("\n9. Checking monitoring status...")
        response = await client.get(f"{base_url}/monitoring/status")
        if response.status_code == 200:
            status = response.json()
            print(f"✅ Monitoring status:")
            print(f"   Service status: {status['service_status']}")
            print(f"   Attached tables: {status['attached_tables']}")
            print(f"   Active queries: {status['active_queries']}")
            print(f"   Pool size: {status['pool_size']}")
        
        # 9. Get Prometheus metrics
        print("\n10. Getting Prometheus metrics...")
        response = await client.get(f"{base_url}/metrics")
        if response.status_code == 200:
            metrics = response.text
            # Count metrics
            metric_lines = [line for line in metrics.split('\n') if line and not line.startswith('#')]
            print(f"✅ Prometheus metrics available: {len(metric_lines)} metrics")
            
            # Show some key metrics
            for line in metrics.split('\n'):
                if 'duckdb_queries_total' in line and not line.startswith('#'):
                    print(f"   {line}")
                    break
        
        # 10. Test concurrent queries
        print("\n11. Testing concurrent queries...")
        
        async def run_concurrent_query(query_id: int):
            async with httpx.AsyncClient(timeout=30.0) as concurrent_client:
                query_request = {
                    "sql": f"SELECT COUNT(*) as count FROM demo_table WHERE id % {query_id + 1} = 0",
                    "timeout": 30.0
                }
                response = await concurrent_client.post(
                    f"{base_url}/q",
                    params={"fmt": "json"},
                    json=query_request
                )
                return response.status_code, query_id
        
        # Run 5 concurrent queries
        tasks = [run_concurrent_query(i) for i in range(5)]
        results = await asyncio.gather(*tasks)
        
        successful_queries = sum(1 for status, _ in results if status == 200)
        print(f"✅ Concurrent queries: {successful_queries}/5 successful")
        
        # 11. Clean up
        print("\n12. Cleaning up...")
        response = await client.delete(f"{base_url}/tables/demo_table")
        if response.status_code == 200:
            print("✅ Table detached successfully")
        
        print("\n🎉 Demo completed successfully!")
        print("=" * 50)


if __name__ == "__main__":
    print("Delta Lake DuckDB API Demo")
    print("Make sure the API service is running on http://localhost:8000")
    print("Start it with: uv run python -m data_marketplace.api.main")
    print()
    
    try:
        asyncio.run(demo_api_usage())
    except KeyboardInterrupt:
        print("\n👋 Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
