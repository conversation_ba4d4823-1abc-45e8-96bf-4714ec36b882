# SPDX-FileCopyrightText: Terrafloww Labs, 2025

# Multi-stage build for the Delta Lake API service
FROM python:3.11-slim as builder

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Install uv for fast Python package management
RUN pip install uv

# Set working directory
WORKDIR /app

# Copy dependency files
COPY pyproject.toml uv.lock ./

# Install dependencies
RUN uv sync --frozen --no-dev

# Production stage
FROM python:3.11-slim as production

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN groupadd -r duckdb && useradd -r -g duckdb duckdb

# Set working directory
WORKDIR /app

# Copy virtual environment from builder
COPY --from=builder /app/.venv /app/.venv

# Copy source code
COPY src/ ./src/
COPY pyproject.toml ./

# Create cache directory with proper permissions
RUN mkdir -p /tmp/duckdb_cache && chown -R duckdb:duckdb /tmp/duckdb_cache

# Create data directory for ephemeral storage
RUN mkdir -p /app/data && chown -R duckdb:duckdb /app/data

# Switch to non-root user
USER duckdb

# Set environment variables
ENV PATH="/app/.venv/bin:$PATH"
ENV PYTHONPATH="/app/src"
ENV DUCKDB_CACHE_DIR="/tmp/duckdb_cache"
ENV DUCKDB_MEMORY_LIMIT="4GB"
ENV DUCKDB_THREADS="2"

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Expose port
EXPOSE 8000

# Run the application
CMD ["python", "-m", "uvicorn", "data_marketplace.api.main:app", "--host", "0.0.0.0", "--port", "8000"]
